# Log Generator

Tool tự động tạo file log dựa vào cấu hình YAML.

## T<PERSON>h năng

- <PERSON><PERSON>c cấu hình từ file YAML
- Sinh timestamp ngẫu nhiên trong khoảng thời gian được định nghĩa
- Thay thế các placeholder (UUID, timestamp, API ID, ...) bằng giá trị ngẫu nhiên
- Sinh file log theo format và tạo tên file theo pattern
- Nén file log bằng gzip
- Ước tính số dòng log cần thiết để đạt kích thước file sau nén
- CLI interface để điều khiển quá trình sinh log

## Cấu trúc thư mục

```
log_generator/
  ├── config.yaml       # Cấu hình
  ├── src/              # Mã nguồn
  │   ├── config_loader.py  # Module đọc và phân tích cấu hình
  │   ├── log_generator.py  # Module sinh log
  │   ├── log_writer.py     # Module ghi và nén file
  │   ├── cli.py            # Command-line interface
  │   └── tests/            # Unit tests
  ├── output/           # Thư mục chứa file log output
  └── docs/             # Tài liệu
      └── format.md     # Định dạng log
```

## Cài đặt

```bash
# Cài đặt các package cần thiết
pip install pyyaml
```

## Sử dụng

### Sinh log từ cấu hình mặc định

```bash
python src/cli.py
```

### Tùy chỉnh tham số

```bash
# Chỉ định file cấu hình khác
python src/cli.py --config path/to/config.yaml

# Chỉ định thư mục output
python src/cli.py --output-dir path/to/output

# Ghi đè số lượng file cần tạo
python src/cli.py --file-count 100

# Ghi đè kích thước file sau khi nén (MB)
python src/cli.py --size-mb 50

# Chỉ định ứng dụng trong cấu hình
python src/cli.py --app-name apf-dsn-flow

# Chạy thử để xem thông tin mà không tạo file
python src/cli.py --dry-run
```

## Cấu hình

File `config.yaml` có cấu trúc như sau:

```yaml
logs:
  - file_count: 1000               # Số lượng file log cần tạo
  - file_size_after_compression: 100 MB  # Kích thước file sau khi nén
timestamp:
  from: 2025-05-10 00:00:00        # Thời gian bắt đầu
  to: 2025-05-17 23:59:00          # Thời gian kết thúc
  format: "%Y-%m-%d %H:%M:%S,%f"   # Định dạng timestamp
foundation:
  apf-dsn-flow:                    # Tên ứng dụng
   - format: __TIMESTAMP__\tI\tapf-dsn-flow\tapi-msg\t79\t24\t[__UUID__][DataSendRule#102][Functions#101:DataSendRule#101:2075:1860:1854] [__API_ID__:__PF__:* __API_NAME__] 1-#$7 dataSend fin status=0 {"ReturnCode":"500C00", "Message":"Accepted", "Data":{}}
   - path: st/base/logs/online/api-msg  # Thư mục chứa logs
   - file_name: apf-dsn-flow_apf-dsn-ext-01_%y%m%d%H%M%S  # Pattern tên file
   - file_extension: .log.gz        # Đuôi file log
   - log_line_use_rate: 1           # Tỉ lệ log match điều kiện
   - variables:                     # Các biến thay thế
    - key: __API_ID__
      value: [130_2_99,160_2_99]
    - key: __PF__
      value: [18,20,24]
    - key: __API_NAME__
      value: ["api1","api2"]
    - key
```

## Các placeholder

Tool sẽ tự động thay thế các placeholder sau trong format log:

- `__TIMESTAMP__`: Thay thế bằng timestamp ngẫu nhiên trong khoảng cấu hình
- `__UUID__`: Thay thế bằng UUID random
- Các biến khác được định nghĩa trong phần `variables`

## Lưu ý

- Tất cả các file log sẽ được nén theo định dạng gzip
- Kích thước file sau khi nén sẽ được ước tính dựa trên mẫu log
- Timestamp trong tên file có thể khác với timestamp trong nội dung log

logs:

  - file_size_after_compression: 1 MB
timestamp:
  from: 2025-05-10 00:00:00
  to: 2025-05-17 23:59:00
  # 2025-05-21 15:00:15,406140000
  format: "%Y-%m-%d %H:%M:%S,%f"
internal_:
  char_count: 17
  prefix: VIN000000000
  vin_start: 0000001
  vin_end: 9999999
  count: 1000000 # Số lượng vin

foundation:
  apf-dsn-flow:
    file_count: 1
    format: >-
      __TIMESTAMP__ I apf-dsn-flow api-msg 79 24 [__UUID__][DataSendRule#102][Functions#101:DataSendRule#101:2075:1860:1854] [__API_ID__:__PF__:* __API_NAME__] __BODY__
    path: st/base/logs/online/api-msg  # Folder chứa logs
    # apf-dsn-flow_apf-dsn-ext-01-78f8fcb4b4-9xb6f_20250318071310.gz
    file_name: apf-dsn-flow_apf-dsn-ext-01_%y%m%d%H%M%S
    file_extension: .log  # Đuôi file log
    log_line_use_rate: 1  # Tỉ lệ log match điều kiện
    variables:
      __API_ID__: ["130_2_99", "160_2_99"]
      __PF__: [18, 20, 24]
      __API_NAME__: ["api1", "api2"]
      __BODY__:
        - >-
           run str data={"Vin":"JMZDR1WBJ00222009","AppRequestNo":"9f3f52c1-c22d-3b7b-81a2-e86002a9e158","RequestResult":{"Status":0,"Phase":4},"RemoteControlResult":{"TransmissionFactor":33,"DcmNumber":"9999999999900000","BsId":"FFFF","SId":"FFFF","NId":"FFFF","DcmDormantDatetime":"2025-04-15T00:09:02Z","OccurrenceTime":"2025-04-15T00:08:23Z","PositionInfoCategory":4,"PositionInfo":{"AcquisitionDatetime":"2025-04-15T00:08:48Z","Longitude":132.500104,"Latitude":34.380486,"UsbPositionAccuracy":180,"GeodeticReferenceSystem":0},"TableVersion":"01","TimeInformation":{"CenterTimeStamp":"2025-04-15T00:08:16Z","RequestReceptionTime":"2025-04-15T00:08:51Z","OperationEndTime":"0000-00-00T00:00:00Z","StandbyStartTime":"0000-00-00T00:00:00Z"},"DoorLockResult":"FD"}} optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin": __INTERNAL_VIN__,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBD","lastName":"TestBD","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-GB","internalUserId":54,"option":"{}"},{"firstName":"UserBF","lastName":"TestBF","country":"DE","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-DE","internalUserId":56,"option":"{}"},{"firstName":"UserBI","lastName":"TestBI","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":0,"locale":"en-GB","internalUserId":59,"option":"{}"},{"firstName":"UserBK","lastName":"TestBK","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-FR","internalUserId":61,"option":"{}"},{"firstName":"UserBP","lastName":"TestBP","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-FR","internalUserId":66,"option":"{}"}]}}
        - >-
          communicate str data={"Vin":"JMZDR1WBJ00222009","AppRequestNo":"76d5d8f3-f254-3ad6-b7da-23dd41b9f44b","RequestResult":{"Status":0,"Phase":4},"RemoteControlResult":{"TransmissionFactor":33,"DcmNumber":"9999999999900000","BsId":"FFFF","SId":"FFFF","NId":"FFFF","DcmDormantDatetime":"2025-04-18T02:31:49Z","OccurrenceTime":"2025-04-18T02:31:37Z","PositionInfoCategory":4,"PositionInfo":{"AcquisitionDatetime":"2025-04-18T02:31:38Z","Longitude":132.499514,"Latitude":34.380382,"UsbPositionAccuracy":270,"GeodeticReferenceSystem":0},"TableVersion":"01","TimeInformation":{"CenterTimeStamp":"2025-04-18T02:31:30Z","RequestReceptionTime":"2025-04-18T02:31:40Z","OperationEndTime":"0000-00-00T00:00:00Z","StandbyStartTime":"0000-00-00T00:00:00Z"},"DoorLockResult":"01"}} optional=optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin":__INTERNAL_VIN__,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBI","lastName":"TestBI","country":"DE","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":0,"locale":"en-DE","internalUserId":59,"option":"{}"}]}}}
        - >-
          720-#$7 dataSend fin
        - >-
          1-#$7 dataSend failed. status=202 contents={"ReturnCode":"011000","AppRequestNo":"6c368788-7a48-3022-b302-95673beb0212","Message":"Session with CDC not established or CDC responds STACK"}
        
  apf-msg-flow:
    file_count: 1
    format: >-
      __TIMESTAMP__	I	apf-msg-idle-01-cf7b4ddcb-9sv27	apf-msg-flow	62	17	[__UUID__][Functions#1002][3707] [__API_ID__:__PF__:* __API_NAME__] __BODY__
    path: st/base/logs/online/adf-dsn # Folder chứa logs
    file_name: apf-dsn-flow_apf-dsn-ext-01_%y%m%d%H%M%S
    file_extension: .log  # Đuôi file log
    log_line_use_rate: 1  # Tỉ lệ log match điều kiện
    variables:
      __API_ID__: ["130_2_99", "160_2_99"]
      __PF__: [18, 20, 24]
      __API_NAME__: ["api1", "api2"]
      __BODY__:
        - >-
         run str data={"Vin":"JMZDR1WBJ00222009","AppRequestNo":"9f3f52c1-c22d-3b7b-81a2-e86002a9e158","RequestResult":{"Status":0,"Phase":4},"RemoteControlResult":{"TransmissionFactor":33,"DcmNumber":"9999999999900000","BsId":"FFFF","SId":"FFFF","NId":"FFFF","DcmDormantDatetime":"2025-04-15T00:09:02Z","OccurrenceTime":"2025-04-15T00:08:23Z","PositionInfoCategory":4,"PositionInfo":{"AcquisitionDatetime":"2025-04-15T00:08:48Z","Longitude":132.500104,"Latitude":34.380486,"UsbPositionAccuracy":180,"GeodeticReferenceSystem":0},"TableVersion":"01","TimeInformation":{"CenterTimeStamp":"2025-04-15T00:08:16Z","RequestReceptionTime":"2025-04-15T00:08:51Z","OperationEndTime":"0000-00-00T00:00:00Z","StandbyStartTime":"0000-00-00T00:00:00Z"},"DoorLockResult":"FD"}} optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin":__INTERNAL_VIN__,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBD","lastName":"TestBD","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-GB","internalUserId":54,"option":"{}"},{"firstName":"UserBF","lastName":"TestBF","country":"DE","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-DE","internalUserId":56,"option":"{}"},{"firstName":"UserBI","lastName":"TestBI","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":0,"locale":"en-GB","internalUserId":59,"option":"{}"},{"firstName":"UserBK","lastName":"TestBK","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-FR","internalUserId":61,"option":"{}"},{"firstName":"UserBP","lastName":"TestBP","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+************","middleName":"User","userType":1,"locale":"en-FR","internalUserId":66,"option":"{}"}]}}
    
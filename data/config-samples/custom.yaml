logs:
  - file_count: 5
  - file_size_after_compression: 1 MB
timestamp:
  from: 2025-06-01 00:00:00
  to: 2025-06-10 23:59:00
  format: "%Y-%m-%d %H:%M:%S,%f"
foundation:
  apf-dsn-flow:
    - format: "__TIMESTAMP__\tI\tapf-dsn-flow\tapi-msg\t79\t24\t[__UUID__][DataSendRule#102][Functions#101:DataSendRule#101:2075:1860:1854] [__API_ID__:__PF__:* __API_NAME__] 1-#$7 dataSend fin status=0 {\"ReturnCode\":\"500C00\", \"Message\":\"Accepted\", \"Data\":{}}"
    - path: custom/logs
    - file_name: apf-dsn-flow_apf-dsn-ext-01_%y%m%d%H%M%S
    - file_extension: .log.gz
    - log_line_use_rate: 1
    - variables:
        - key: __API_ID__
          value: [130_2_99,160_2_99]
        - key: __PF__
          value: [18,20,24]
        - key: __API_NAME__
          value: ["api1","api2","api3","api4","api5"]

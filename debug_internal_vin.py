#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config_loader import ConfigLoader
from log_generator import LogGenerator

# Test internal VIN replacement
config = ConfigLoader("config.yaml")
generator = LogGenerator(config)

# Test string with __INTERNAL_VIN__
test_string = 'data={"internalVin":__INTERNAL_VIN__,"vin":"test"}'
print(f"Original: {test_string}")

# Test internal VIN values
internal_vin_values = config.get_internal_vin_values()
print(f"Internal VIN values: {internal_vin_values}")

# Test _generate_internal_vin method
internal_vin = generator._generate_internal_vin()
print(f"Generated internal VIN: {internal_vin}")

# Test _replace_variables method
variables = {"test": "value"}
result = generator._replace_variables(test_string, variables)
print(f"After _replace_variables: {result}")

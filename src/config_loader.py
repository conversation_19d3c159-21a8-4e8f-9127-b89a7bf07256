#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration loader for log generator.
This module reads and parses the YAML configuration file.
"""

import yaml
import os
from typing import Dict, Any, List, Union


class ConfigLoader:
    """
    YAML configuration loader for log generator.
    """

    def __init__(self, config_path: str):
        """
        Initialize config loader with config file path.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file.
        
        Returns:
            Dict containing the configuration
        """
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"設定ファイルが見つかりません: {self.config_path}")
            
        with open(self.config_path, 'r') as file:
            try:
                return yaml.safe_load(file)
            except yaml.YAMLError as e:
                raise ValueError(f"YAMLファイルの解析エラー: {e}")
    
    def get_log_config(self) -> Dict[str, Any]:
        """
        Get log configuration.
        
        Returns:
            Dict containing log configuration
        """
        return self.config.get('logs', [])
    
    def get_timestamp_config(self) -> Dict[str, str]:
        """
        Get timestamp configuration.
        
        Returns:
            Dict containing timestamp configuration
        """
        return self.config.get('timestamp', {})
    
    def get_foundation_config(self) -> Dict[str, Any]:
        """
        Get foundation configuration.
        
        Returns:
            Dict containing foundation configuration with app names as keys
        """
        foundation = self.config.get('foundation', {})
        # Ensure foundation is a dict and not None
        if foundation is None:
            return {}
        # If foundation is already a dict, return it directly
        if isinstance(foundation, dict):
            return foundation
            
        # If we reach here, it's an unexpected type
        print(f"Warning: Unexpected foundation type: {type(foundation)}. Expected dict.")
        return {}
    
    def get_log_format(self, app_name: str) -> str:
        """
        Get log format string for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log format string
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ""
            
        # Trả về trực tiếp giá trị format từ cấu hình ứng dụng
        return app_config.get('format', '')
    
    def get_log_path(self, app_name: str) -> str:
        """
        Get log path for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log path
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return "logs"  # Default log path
            
        return app_config.get('path', 'logs')  # Default log path
    
    def get_file_name_pattern(self, app_name: str) -> str:
        """
        Get file name pattern for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            File name pattern
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ""
            
        return app_config.get('file_name', '')
    
    def get_file_extension(self, app_name: str) -> str:
        """
        Get file extension for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            File extension
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ".log"  # Default extension
            
        return app_config.get('file_extension', '.log')  # Default extension
    
    def get_log_line_use_rate(self, app_name: str) -> float:
        """
        Get log line use rate for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log line use rate
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return 1.0
            
        return float(app_config.get('log_line_use_rate', 1.0))
    
    def get_variables(self, app_name: str) -> dict:
        """
        Get variables for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Dict containing variables and their values
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return {}
            
        # Lấy toàn bộ variables từ cấu hình ứng dụng
        variables = app_config.get('variables', {})
        
        # Nếu variables là list, chuyển đổi thành dict
        if isinstance(variables, list):
            return {var['key']: var['value'] for var in variables if 'key' in var and 'value' in var}
            
        return variables if isinstance(variables, dict) else {}
    
    def get_file_count(self, app_name: str = None) -> int:
        """
        Get number of log files to generate for a specific application.
        
        Args:
            app_name: Name of the application. If None, returns the global file count.
            
        Returns:
            Number of log files to generate
        """
        # If app_name is provided, try to get file_count from the app's config
        if app_name:
            foundation = self.get_foundation_config()
            app_config = foundation.get(app_name, {})
            if 'file_count' in app_config:
                return int(app_config['file_count'])
        
        # Fall back to global file_count in logs section
        logs = self.get_log_config()
        for log in logs:
            if isinstance(log, dict) and 'file_count' in log:
                return int(log['file_count'])
                
        return 1  # Default to 1 if not specified
    
    def get_file_size_after_compression(self) -> str:
        """
        Get target file size after compression.
        
        Returns:
            Target file size after compression
        """
        logs = self.get_log_config()
        for log in logs:
            if 'file_size_after_compression' in log:
                return log['file_size_after_compression']
        return "0"  # Default to 0 if not specified
        
    def get_vin_config(self) -> Dict[str, Any]:
        """
        Get VIN (Vehicle Identification Number) configuration.
        
        Returns:
            Dict containing VIN configuration with keys:
            - char_count: Number of characters in the VIN
            - prefix: Prefix for VIN generation
            - vin_start: Starting number for VIN sequence
            - vin_end: Ending number for VIN sequence
            - count: Total count of VINs to generate
        """
        return self.config.get('vin', {})


if __name__ == "__main__":
    # テスト用コード
    config = ConfigLoader("../config.yaml")
    print("Timestamp config:", config.get_timestamp_config())
    print("Log format:", config.get_log_format("apf-dsn-flow"))
    print("Variables:", config.get_variables("apf-dsn-flow"))

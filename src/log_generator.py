#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Log generator module.
This module is responsible for generating log lines based on the configuration.
Supports multi-threaded log generation for improved performance.
"""

import concurrent.futures
import os
import random
import re
import string
import threading
import time
import uuid
from datetime import datetime, timedelta
import random
import re
import string
import threading
import time
import uuid
from dataclasses import dataclass
from typing import Any, Dict, Generator, List, Optional, Tuple, Union

from src.config_loader import ConfigLoader


class LogGenerator:
    """
    Generate log lines based on configuration.
    """
    
    def __init__(self, config):
        """
        Initialize log generator with configuration.
        
        Args:
            config: ConfigLoader instance
        """
        self.config = config
        self.timestamp_format = config.get_timestamp_config().get('format', "%Y-%m-%d %H:%M:%S,%f")
        self.timestamp_from = config.get_timestamp_config().get('from', "")
        self.timestamp_to = config.get_timestamp_config().get('to', "")
        self._parse_timestamp_range()
        
        # VIN generation configuration and thread safety
        self.vin_config = config.get_vin_config()
        self.vin_lock = threading.Lock()
        self.current_vin_index = 0

        # Sequential timestamp generation
        self.timestamp_lock = threading.Lock()
        self.current_timestamp_index = 0
        
        # Thread-local storage for random number generation
        self.local = threading.local()
        
    def _init_thread_local(self):
        """Initialize thread-local storage for random number generation."""
        if not hasattr(self.local, 'random'):
            self.local.random = random.Random()  # Each thread gets its own random instance
            
    def _parse_timestamp_range(self):
        """
        Parse timestamp range from configuration.
        Handle both string and datetime objects from YAML parser.
        """
        try:
            # Handle both string and datetime objects for timestamp_from
            if isinstance(self.timestamp_from, datetime):
                self.timestamp_from_dt = self.timestamp_from
            else:
                self.timestamp_from_dt = datetime.strptime(
                    self.timestamp_from, "%Y-%m-%d %H:%M:%S"
                )
            
            # Handle both string and datetime objects for timestamp_to
            if isinstance(self.timestamp_to, datetime):
                self.timestamp_to_dt = self.timestamp_to
            else:
                self.timestamp_to_dt = datetime.strptime(
                    self.timestamp_to, "%Y-%m-%d %H:%M:%S"
                )
        except ValueError as e:
            raise ValueError(f"タイムスタンプの形式が無効です: {e}")
        except Exception as e:
            raise ValueError(f"タイムスタンプの処理中にエラーが発生しました: {e}")
            
    def _generate_random_timestamp(self) -> datetime:
        """
        Generate random timestamp within the configured range.
        
        Returns:
            Random timestamp as datetime object
        """
        # Use thread-local random number generator
        self._init_thread_local()
        delta = self.timestamp_to_dt - self.timestamp_from_dt
        delta_seconds = delta.total_seconds()
        random_seconds = self.local.random.uniform(0, delta_seconds)
        
        return self.timestamp_from_dt + timedelta(seconds=random_seconds)
        
    def _get_timestamp(self, index: int, base_timestamp: Optional[datetime] = None) -> datetime:
        """
        Generate timestamp that increases by milliseconds from a base timestamp.
        
        Args:
            base_timestamp: Base timestamp to start from
            index: Index to increment by milliseconds
            
        Returns:
            Sequential timestamp as datetime object
        """
        if base_timestamp is None:
            base_timestamp = self.timestamp_from_dt
        return base_timestamp + timedelta(milliseconds=index)

    def _generate_sequential_timestamp(self) -> datetime:
        """
        Generate sequential timestamp with small increments for realistic log timing.
        Thread-safe implementation that ensures sequential timestamps.

        Returns:
            Sequential timestamp as datetime object
        """
        with self.timestamp_lock:
            self.current_timestamp_index += 1
            # Use small random increments (1-100ms) for realistic log timing
            increment_ms = random.randint(1, 100)
            return self.timestamp_from_dt + timedelta(milliseconds=self.current_timestamp_index * increment_ms)

    def _format_timestamp(self, dt: datetime) -> str:
        """
        Format datetime object to string.
        
        Args:
            dt: Datetime object to format
            
        Returns:
            Formatted timestamp string
        """
        timestamp = dt.strftime(self.timestamp_format)
        # ミリ秒はfオプションで9桁まで出力されるため、必要なら切り捨て
        if "%f" in self.timestamp_format:
            timestamp = timestamp.replace(dt.strftime("%f"), dt.strftime("%f")[:6])
        return timestamp
        
    def _generate_vin(self) -> str:
        """
        Generate a VIN based on configuration.
        Thread-safe implementation that ensures unique VINs across threads.
        
        Returns:
            Generated VIN string
        """
        # VIN configuration
        char_count = self.vin_config.get('char_count', 17)
        prefix = self.vin_config.get('prefix', 'VIN')
        vin_start = int(str(self.vin_config.get('vin_start', 1)).replace('0', ''))
        vin_end = int(str(self.vin_config.get('vin_end', 9999999)).replace('0', ''))
        
        # Thread-safe increment of VIN index
        with self.vin_lock:
            self.current_vin_index += 1
            vin_value = vin_start + (self.current_vin_index % (vin_end - vin_start + 1))
        
        # Format the suffix with leading zeros
        suffix_len = char_count - len(prefix)
        if suffix_len <= 0:
            return prefix[:char_count]
            
        vin_format = f"{{:0{suffix_len}d}}"
        suffix = vin_format.format(vin_value)
        
        return prefix + suffix[-suffix_len:]

    def _generate_internal_vin(self) -> str:
        """
        Generate an internal VIN by randomly selecting from configured values.

        Returns:
            Random internal VIN string from configuration
        """
        internal_vin_values = self.config.get_internal_vin_values()
        if not internal_vin_values:
            return "IV000000000000000"  # Default fallback
        return random.choice(internal_vin_values)

    def _replace_variables(self, log_format: str, variables: Dict[str, Union[List[Union[str, int]], str, int, bool, dict]]) -> str:
        """
        Replace variables in log format with random values from the configuration.
        
        Args:
            log_format: Log format string with variables
            variables: Dictionary of variables and their possible values.
                     Can be a list of values (randomly selected) or a single value.
        
        Returns:
            Log string with variables replaced
        """
        result = log_format
        
        # Generate UUID
        result = result.replace("__UUID__", str(uuid.uuid4()))
        
        # Generate timestamp
        timestamp = self._format_timestamp(self._generate_random_timestamp())
        result = result.replace("__TIMESTAMP__", timestamp)
        
        # Generate VIN
        if "__VIN__" in result:
            result = result.replace("__VIN__", self._generate_vin())

        # Generate Internal VIN
        if "__INTERNAL_VIN__" in result:
            result = result.replace("__INTERNAL_VIN__", self._generate_internal_vin())
        
        # Special handling for __BODY__ - select one random value from the list
        if "__BODY__" in result and "__BODY__" in variables:
            body_values = variables["__BODY__"]
            if isinstance(body_values, list) and len(body_values) > 0:
                selected_body = random.choice(body_values)
                result = result.replace("__BODY__", str(selected_body))
        
        # Replace other variables
        for var_name, value in variables.items():
            # Skip if already processed or None
            if var_name == "__BODY__" or value is None:
                continue
                
            # Handle list of values (random selection)
            if isinstance(value, list) and len(value) > 0:
                random_value = random.choice(value)
                # Use regex to replace whole word matches only
                pattern = re.compile(rf'\b{re.escape(var_name)}\b')
                result = pattern.sub(str(random_value), result)
            # Handle direct value (string, number, boolean, etc.)
            else:
                # Use regex to replace whole word matches only
                pattern = re.compile(rf'\b{re.escape(var_name)}\b')
                result = pattern.sub(str(value), result)
                
        return result
        
    def generate_log_line(self, app_name: str) -> str:
        """
        Generate a log line for the specified application.
        
        Args:
            app_name: Name of the application
        
        Returns:
            Generated log line
        """
        # Generate a sequential timestamp for the log line
        timestamp = self._generate_sequential_timestamp()
        return self.generate_log_line_with_timestamp(app_name, timestamp)
        
    def generate_log_line_with_timestamp(self, app_name: str, timestamp: datetime) -> str:
        """
        Generate a log line for the specified application with a given timestamp.

        Args:
            app_name: Name of the application
            timestamp: Timestamp to use

        Returns:
            Generated log line
        """
        log_format = self.config.get_log_format(app_name)
        variables = self.config.get_variables(app_name)

        # Replace timestamp first
        formatted_timestamp = self._format_timestamp(timestamp)
        result = log_format.replace("__TIMESTAMP__", formatted_timestamp)

        # Use the centralized variable replacement method
        return self._replace_variables(result, variables)
        
    def _generate_log_lines(self, app_name: str, count: int) -> Generator[str, None, None]:
        """
        Generate log lines as a generator to save memory.
        
        Args:
            app_name: Name of the application
            count: Number of lines to generate
            
        Yields:
            Generated log lines one by one
        """
        for _ in range(count):
            yield self.generate_log_line(app_name)
    
    def generate_multiple_lines(self, app_name: str, count: int, base_timestamp: Optional[datetime] = None) -> List[str]:
        """
        Generate multiple log lines for the specified application.
        
        Args:
            app_name: Name of the application
            count: Number of lines to generate
            base_timestamp: Base timestamp to start from
            
        Returns:
            List of generated log lines
        """
        return list(self._generate_log_lines(app_name, count))
    
    def _generate_chunk(self, app_name: str, chunk_size: int) -> List[str]:
        """Generate a chunk of log lines."""
        self._init_thread_local()
        return [self.generate_log_line(app_name) for _ in range(chunk_size)]
    
    def _generate_log_lines_threaded(self, app_name: str, total_lines: int, 
                                  max_workers: Optional[int] = None) -> Generator[str, None, None]:
        """
        Generate log lines in parallel using multiple threads.
        
        Args:
            app_name: Name of the application
            total_lines: Total number of lines to generate
            max_workers: Maximum number of worker threads to use.
            
        Yields:
            Generated log lines one by one
        """
        if max_workers is None:
            max_workers = min(32, (os.cpu_count() or 1) + 4)
        
        # Calculate lines per worker
        chunk_size = max(1000, total_lines // (max_workers * 10))  # Smaller chunks for better load balancing
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit initial tasks
            futures = []
            remaining = total_lines
            
            while remaining > 0:
                current_chunk = min(chunk_size, remaining)
                future = executor.submit(self._generate_chunk, app_name, current_chunk)
                futures.append((future, current_chunk))
                remaining -= current_chunk
            
            # Process results as they complete
            for future, chunk_size in futures:
                try:
                    for line in future.result():
                        yield line
                except Exception as exc:
                    print(f'Generated an exception: {exc}')
    
    def generate_multiple_lines_threaded(self, app_name: str, count: int, workers: int = 4, base_timestamp: Optional[datetime] = None) -> List[str]:
        """
        Generate multiple log lines in parallel using multiple threads.
        
        Args:
            app_name: Name of the application
            count: Number of log lines to generate
            workers: Number of worker threads to use for parallel generation
            base_timestamp: Optional base timestamp for log entries
            
        Returns:
            List of generated log lines
        """
        return list(self._generate_log_lines_threaded(app_name, count, workers))
        
    def generate_log_line(self, app_name: str) -> str:
        """
        Generate a log line for the specified application.
        
        Args:
            app_name: Name of the application
        
        Returns:
            Generated log line
        """
        # Generate a sequential timestamp for the log line
        timestamp = self._generate_sequential_timestamp()
        return self.generate_log_line_with_timestamp(app_name, timestamp)
        
    def generate_log_line_with_timestamp(self, app_name: str, timestamp: datetime) -> str:
        """
        Generate a log line for the specified application with a given timestamp.

        Args:
            app_name: Name of the application
            timestamp: Timestamp to use

        Returns:
            Generated log line
        """
        log_format = self.config.get_log_format(app_name)
        variables = self.config.get_variables(app_name)

        # Replace timestamp first
        formatted_timestamp = self._format_timestamp(timestamp)
        result = log_format.replace("__TIMESTAMP__", formatted_timestamp)

        # Use the centralized variable replacement method
        return self._replace_variables(result, variables)
        
    def _generate_log_lines(self, app_name: str, count: int) -> Generator[str, None, None]:
        """
        Generate log lines as a generator to save memory.
        
        Args:
            app_name: Name of the application
            count: Number of lines to generate
            
        Yields:
            Generated log lines one by one
        """
        for _ in range(count):
            yield self.generate_log_line(app_name)
    
    def generate_multiple_lines(self, app_name: str, count: int, base_timestamp: Optional[datetime] = None) -> List[str]:
        """
        Generate multiple log lines for the specified application.
        
        Args:
            app_name: Name of the application
            count: Number of lines to generate
            base_timestamp: Base timestamp to start from
            
        Returns:
            List of generated log lines
        """
        return list(self._generate_log_lines(app_name, count))
    
    def _generate_chunk(self, app_name: str, chunk_size: int) -> List[str]:
        """Generate a chunk of log lines."""
        self._init_thread_local()
        return [self.generate_log_line(app_name) for _ in range(chunk_size)]
    
    def _generate_log_lines_threaded(self, app_name: str, total_lines: int, 
                                  max_workers: Optional[int] = None) -> Generator[str, None, None]:
        """
        Generate log lines in parallel using multiple threads.
        
        Args:
            app_name: Name of the application
            total_lines: Total number of lines to generate
            max_workers: Maximum number of worker threads to use.
            
        Yields:
            Generated log lines one by one
        """
        if max_workers is None:
            max_workers = min(32, (os.cpu_count() or 1) + 4)
        
        # Calculate lines per worker
        chunk_size = max(1000, total_lines // (max_workers * 10))  # Smaller chunks for better load balancing
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit initial tasks
            futures = []
            remaining = total_lines
            
            while remaining > 0:
                current_chunk = min(chunk_size, remaining)
                future = executor.submit(self._generate_chunk, app_name, current_chunk)
                futures.append((future, current_chunk))
                remaining -= current_chunk
            
            # Process results as they complete
            for future, chunk_size in futures:
                try:
                    for line in future.result():
                        yield line
                except Exception as exc:
                    print(f'Generated an exception: {exc}')
    
    def generate_multiple_lines_threaded(self, app_name: str, count: int, workers: int = 4, 
                                      base_timestamp: Optional[datetime] = None) -> List[str]:
        """
        Generate multiple log lines in parallel using multiple threads.
        
        Args:
            app_name: Name of the application
            count: Number of lines to generate
            workers: Number of worker threads to use
            base_timestamp: Base timestamp to start from
            
        Returns:
            List of generated log lines
        """
        return list(self._generate_log_lines_threaded(app_name, count, workers))


if __name__ == "__main__":
    # テスト用コード
    from config_loader import ConfigLoader
    
    config = ConfigLoader("../config.yaml")
    generator = LogGenerator(config)
    
    # 複数行を生成してみる
    log_lines = generator.generate_multiple_lines("apf-dsn-flow", 5)
    for line in log_lines:
        print(line)
